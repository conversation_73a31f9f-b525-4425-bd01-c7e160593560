import { BaseMarker } from "../BaseMarker";
import maplibregl from "maplibre-gl";
import emitter from "@/utils/emitter";
import mapConfig from "@/config/engine/maplibre/map.config.js";
import TrafficAccidentMarkerVue from "@/views/gdMap/engine/components/MapLibreEngine/components/TrafficAccidentMarker.vue";
import TrafficDeviceMarkerVue from "@/views/gdMap/engine/components/MapLibreEngine/components/TrafficDeviceMarker.vue";
import TrafficAlarmMarkerVue from "@/views/gdMap/engine/components/MapLibreEngine/components/TrafficAlarmMarker.vue";

/**
 * 保通标记类
 * 继承自BaseMarker
 */
class TrafficMarker extends BaseMarker {
	static MAP_KEY = "TrafficMarkerMap";
	static HANDLER_ZOOM_KEY = `_${this.name}ZoomHandler`;

	/**
	 * 渲染
	 * @param {Object} map - MapLibre地图实例
	 * @param {String} sourceId - 数据源ID
	 * @param {Object} options - 渲染选项
	 * @returns {Object} 渲染结果
	 */
	static render(map, sourceId, options = {}) {
		const { data } = options;

		const markersData = this.preprocessData(data);

		this.renderMarkers(map, markersData, options);

		this.updateMarkersSize(map);

		this.setZoomHandler(map);
	}

	/**
	 * 渲染marker集合
	 * @param {Object} map
	 * @param {Array} markers
	 * @param {Object} options
	 */
	static renderMarkers(map, markersData, options) {
		this.removeMarkers(map);

		markersData.forEach((markerData) => {
			this.createMarker(map, markerData);
		});
	}

	/**
	 * 创建单个marker
	 * @param {Object} map
	 * @param {Object} markerData
	 */
	static createMarker(map, markerData) {}

	/**
	 * 清除
	 * @param {Object} map
	 */
	static remove(map) {
		this.removeMarkers(map);
		if (map[this.HANDLER_ZOOM_KEY]) {
			map.off("zoom", map[this.HANDLER_ZOOM_KEY]);
		}
	}

	/* 清除所有marker
	 * @param {Object} map
	 */
	static removeMarkers(map) {
		const markers = this.getMarkers(map);
		markers.forEach((marker) => {
			marker.remove();
		});
	}

	/**
	 * 获取所有marker
	 * @param {Object} map
	 * @returns
	 */
	static getMarkers(map) {
		if (map[this.MAP_KEY]) {
			const markers = map[this.MAP_KEY].values();
			return markers;
		}
		return [];
	}

	/**
	 * 数据处理
	 * @param {Array} data
	 * @returns
	 */
	static preprocessData(data) {
		const markers = [];
		data.features.forEach((marker) => {
			markers.push(marker);
		});
		return markers;
	}

	/**
	 * 显示和隐藏相应的marker
	 * @param {*} map
	 * @param {*} isVisible
	 * @param {*} options
	 */
	static setVisibility(map, isVisible, options) {
		const markers = this.getMarkers(map);
		const { category } = options;
		markers.forEach((marker) => {
			const { type } = marker._data.properties;
			if (type === category) {
				marker._element.style.visibility = isVisible ? "visible" : "hidden";
			}
		});
	}

	/**
	 * 监听zoom变化
	 * @param {*} map
	 */
	static setZoomHandler(map) {
		if (map[this.HANDLER_ZOOM_KEY]) {
			map.off("click", map[this.HANDLER_ZOOM_KEY]);
		}

		map[this.HANDLER_ZOOM_KEY] = () => {
			this.updateMarkersSize(map);
		};

		map.on("zoom", map[this.HANDLER_ZOOM_KEY]);
	}

	/**
	 * 更细marker大小
	 * @param {*} map
	 */
	static updateMarkersSize(map) {
		const zoom = map.getZoom();
		// 根据 circle-radius 的插值规则设置 size
		let size;
		if (zoom <= 5) {
			size = 2;
		} else if (zoom <= 9) {
			// 5~9: 2~6
			size = 2 + ((zoom - 5) / (9 - 5)) * (6 - 2);
		} else if (zoom <= 13) {
			// 9~13: 6~28
			size = 6 + ((zoom - 9) / (13 - 9)) * (28 - 6);
		} else if (zoom <= 16) {
			// 13~16: 28~36
			size = 28 + ((zoom - 13) / (16 - 13)) * (36 - 28);
		} else {
			size = 36;
		}

		const markers = this.getMarkers(map);
		markers.forEach((marker) => {
			this.updateElementSize(marker, size * 7);
		});
	}

	/**
	 * 更新marker绑定el的大小
	 * @param {*} marker
	 * @param {*} size
	 */
	static updateElementSize(marker, size) {
		const element = marker.getElement();
		element.style.width = size + "px";
		element.style.height = size + "px";
		element.style.fontSize = size + "px";
	}
}

/**
 * 交通事件标记
 */
export class TrafficAccidentMarker extends TrafficMarker {
	static MAP_KEY = "TrafficAccidentMarkerMap";

	/**
	 * 创建单个marker
	 * @param {Object} map
	 * @param {Object} markerData
	 */
	static createMarker(map, markerData) {
		const {
			geometry: { coordinates },
			properties,
		} = markerData;

		const el = document.createElement("div");

		const app = createApp(TrafficAccidentMarkerVue, {
			properties,
		});
		app.mount(el);

		const marker = new maplibregl.Marker({
			element: el,
		})
			.setLngLat(coordinates)
			.addTo(map);
		marker._data = markerData;

		if (!map[this.MAP_KEY]) {
			map[this.MAP_KEY] = new Map();
		}
		const id = markerData.properties.eventId;
		map[this.MAP_KEY].set(id, marker);

		emitter.$emit("traffic-marker-types", {
			type: "trafficAccident",
			category: { [properties.type]: true },
		});
	}
}

/**
 * 保通设备标记
 */
export class TrafficDeviceMarker extends TrafficMarker {
	static MAP_KEY = "TrafficDeviceMarkerMap";

	/**
	 * 创建单个marker
	 * @param {Object} map
	 * @param {Object} markerData
	 */
	static createMarker(map, markerData) {
		const {
			geometry: { coordinates },
			properties,
		} = markerData;

		const el = document.createElement("div");

		const app = createApp(TrafficDeviceMarkerVue, {
			properties,
		});
		app.mount(el);

		const marker = new maplibregl.Marker({
			element: el,
		})
			.setLngLat(coordinates)
			.addTo(map);
		marker._data = markerData;

		if (!map[this.MAP_KEY]) {
			map[this.MAP_KEY] = new Map();
		}
		const id = markerData.properties.deviceId;
		map[this.MAP_KEY].set(id, marker);

		emitter.$emit("traffic-marker-types", {
			type: "trafficDevice",
			category: { [properties.type]: true },
		});
	}
}

/**
 * 拥堵预警点标记
 */
export class TrafficAlarmMarker extends TrafficMarker {
	static MAP_KEY = "TrafficAlarmMarkerMap";

	/**
	 * 创建单个marker
	 * @param {Object} map
	 * @param {Object} markerData
	 */
	static createMarker(map, markerData) {
		const {
			geometry: { coordinates },
			properties,
		} = markerData;

		const el = document.createElement("div");

		const app = createApp(TrafficAlarmMarkerVue, {
			properties,
		});
		app.mount(el);

		const marker = new maplibregl.Marker({
			element: el,
		})
			.setLngLat(coordinates)
			.addTo(map);
		marker._data = markerData;

		if (!map[this.MAP_KEY]) {
			map[this.MAP_KEY] = new Map();
		}
		const id = markerData.properties.eventId;
		map[this.MAP_KEY].set(id, marker);

		emitter.$emit("traffic-marker-types", {
			type: "trafficAlarm",
			category: { [properties.type]: true },
		});
	}
}
