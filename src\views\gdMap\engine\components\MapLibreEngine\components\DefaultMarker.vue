<template>
	<div ref="markerRef" style="width: 100%; height: 100%">
		<div
			class="common-marker"
			:class="props.markerClass"
			@click.stop="onClickIcon"
			@dblclick.stop="onDblclickIcon"
		>
			<div class="icon">
				<slot name="icon" />
			</div>
		</div>
		<MarkerPanel v-if="isExpanded" :title="props.panelTitle" anchor="right-bottom">
			<template #content>
				<slot name="content" />
			</template>
		</MarkerPanel>
	</div>
</template>

<script setup>
import { v4 } from "uuid";
import emitter from "@/utils/emitter";
import MarkerPanel from "./MarkerPanel.vue";

const props = defineProps({
	panelTitle: String,
	markerClass: String,
	coordinates: Array,
});

const markerId = ref(v4());
const markerRef = ref(null);
const isExpanded = ref(false);

onMounted(() => {
	document.addEventListener("click", handleClickOutside);
	emitter.$on("marker-expand", handleOtherMarkerExpand);
});

onUnmounted(() => {
	document.removeEventListener("click", handleClickOutside);
	emitter.$off("marker-expand", handleOtherMarkerExpand);
});

const onClickIcon = () => {
	if (!isExpanded.value) {
		emitter.$emit("marker-expand", markerId.value);
	}
	isExpanded.value = !isExpanded.value;
};

const onDblclickIcon = () => {
	if (props.coordinates?.length === 2) {
		emitter.$emit("locateToPosition", {
			center: props.coordinates,
			zoom: 14,
		});
	}
};

const handleClickOutside = (event) => {
	if (isExpanded.value && markerRef.value && !markerRef.value.contains(event.target)) {
		isExpanded.value = false;
	}
};

const handleOtherMarkerExpand = (id) => {
	if (id !== markerId.value && isExpanded.value) {
		isExpanded.value = false;
	}
};
</script>

<style lang="scss" scoped>
@import "./common.scss";
</style>
