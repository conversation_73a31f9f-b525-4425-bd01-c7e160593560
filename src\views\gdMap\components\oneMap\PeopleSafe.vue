<template>
  <TrafficCard title="人员安全管理">
    <div class="peoplesafe-root">
      <!-- 上方统计卡片 -->
      <div class="safe-top">
        <div
          class="safe-card card-picker"
          v-for="(item, index) in leftList"
          :key="item.name"
          :class="{
            'is-active': activeTab === index,
          }"
          @click="onClickTab(index)"
        >
          <div class="safe-icon">
            <img :src="item.icon" />
          </div>
          <div class="safe-info">
            <div class="safe-name">{{ item.name }}</div>
            <div class="safe-value">
              <span class="num">{{ data[index].totalNum }}</span>
              <span class="unit">个</span>
            </div>
          </div>
          <div class="border border-left-top" />
          <div class="border border-left-bottom" />
          <div class="border border-right-top" />
          <div class="border border-right-bottom" />
          <div class="dot dot-left-top"></div>
          <div class="dot dot-right-bottom"></div>
        </div>
      </div>
      <!-- 下方审批列表 -->
      <div class="safe-bottom">
        <div class="safe-progress">
          <div class="safe-progress-title">
            <div class="safe-progress-title-l">{{ leftList[activeTab].processText }}</div>
            <div class="safe-progress-title-r">
              <span class="safe-progress-title-v">{{ data[activeTab].handleNum || 0 }} </span>/{{
                data[activeTab].totalNum || 0
              }}
            </div>
          </div>
          <div class="progress-bar">
            <div class="progress-inner" :style="{ width: progressPercent + '%' }"></div>
          </div>
        </div>
        <div class="safe-table">
          <template v-if="activeTab === 0">
            <div class="table-header">
              <span>标段名称</span>
              <span style="flex: 2">工程名称</span>
              <span>审批状态</span>
            </div>
            <div ref="containerRef" class="scroll-container">
              <div class="table-body">
                <div class="table-row" v-for="(row, idx) in data[0].hazardousList || []" :key="idx">
                  <span class="section-name">
                    {{ formatSectionName(row.sectionName) }}
                  </span>
                  <span>{{ row.projectName }}</span>
                  <span class="status">
                    <span
                      class="dot"
                      :class="{
                        passed: row.approvalStatus === 1,
                        pending: row.approvalStatus === 0,
                      }"
                    ></span>
                    <span
                      :class="{
                        'status-passed': row.approvalStatus === 1,
                        'status-pending': row.approvalStatus === 0,
                      }"
                    >
                      {{ row.approvalStatus === 1 ? "已通过" : "审批中" }}
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </template>
          <!-- <template v-if="activeTab === 1">
						<div class="table-header">
							<span>隐患级别</span>
							<span>隐患描述</span>
							<span>整改情况</span>
							<span>责任人</span>
						</div>
						<div ref="containerRef" class="scroll-container">
							<div class="table-body">
								<div class="table-row" v-for="(row, idx) in data[1].hazardousList || []" :key="idx">
									<span>{{ row.hazardLevel }}</span>
									<span>{{ row.hazardName }}</span>
									<span class="status">
										<span
											class="dot"
											:class="{
												passed: row.rectificationStatus === 1,
												pending: row.rectificationStatus === 0,
											}"
										></span>
										<span
											:class="{
												'status-passed': row.rectificationStatus === 1,
												'status-pending': row.rectificationStatus === 0,
											}"
										>
											{{ row.rectificationStatus === 1 ? "已整改" : "未整改" }}
										</span>
									</span>
									<span>{{ row.rectificationPersonName }}</span>
								</div>
							</div>
						</div>
					</template> -->
          <template v-if="activeTab === 1">
            <div class="table-header">
              <span>风险类型</span>
              <span>告警人</span>
              <span>风险时间</span>
              <span>整改情况</span>
            </div>
            <div ref="containerRef" class="scroll-container">
              <div class="table-body">
                <div class="table-row" v-for="(row, idx) in data[1].hazardousList || []" :key="idx">
                  <span>{{ row.alarmType }}</span>
                  <span>{{ row.alarmStaff }}</span>
                  <span>{{ row.alarmTime }}</span>
                  <span class="status">
                    <span
                      class="dot"
                      :class="{
                        passed: row.alarmStatus === 1,
                        pending: row.alarmStatus === 0,
                      }"
                    ></span>
                    <span
                      :class="{
                        'status-passed': row.alarmStatus === 1,
                        'status-pending': row.alarmStatus === 0,
                      }"
                    >
                      {{ row.alarmStatus === 1 ? "已消除" : "告警中" }}
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
    <template #extra>
      <div class="link" @click="onInfo">查看更多 <icon-double-right /></div>
    </template>
  </TrafficCard>

  <!-- 人员管理详情弹窗 -->
  <PeopleManagementModal ref="peopleManagementModalRef" />
</template>

<script setup>
import { nextTick } from "vue";
import request from "@/utils/request";
import { useMarquee } from "@/hooks/useMarquee";
import wdgc from "@/assets/images/people/wdgc.svg";
import yhzg from "@/assets/images/people/yhzg.svg";
import ryaq from "@/assets/images/people/ryaq.svg";
import emitter from "@/utils/emitter";
import PeopleManagementModal from "@/components/detailModal/PeopleManagementModal.vue";

const leftList = [
  {
    icon: wdgc,
    name: "危大工程数",
    url: "/api/screen/board/left/staff/safe/danger",
    processText: "方案审批通过/方案审批中",
  },
  // { icon: yhzg, name: "隐患整改", url: "/api/screen/board/left/staff/safe/rectification" },
  {
    icon: ryaq,
    name: "人员安全风险",
    url: "/api/screen/board/left/staff/safe/risk",
    processText: "已消除风险/风险总量",
  },
];
const activeTab = ref(0);
const data = ref([{}, {}, {}]);
const currentDepartmentId = ref(null);

const progressPercent = computed(() => {
  const handleNum = Number(data.value[activeTab.value].handleNum || 0);
  const totalNum = Number(data.value[activeTab.value].totalNum || 0);
  if (!totalNum) return 0;
  return Math.round((handleNum / totalNum) * 100);
});

const { containerRef, resetScroll } = useMarquee({
  speed: 10, // 滚动速度
  delay: 2000, // 滚动到底部后停顿时间
  step: 1, // 每次滚动的像素
});

watch(
  data,
  () => {
    setTimeout(resetScroll, 300);
  },
  { deep: true }
);

onMounted(() => {
  // 监听区域双击事件
  emitter.$on("division-area-dblclick", handleAreaChange);
  getData();
});

onBeforeUnmount(() => {
  emitter.$off("division-area-dblclick", handleAreaChange);
});

const handleAreaChange = (eventData) => {
  console.log("人员安全管理 - 接收到区域双击事件:", eventData);
  currentDepartmentId.value = eventData.properties.id;
  getData();
};

const getData = () => {
  const params = currentDepartmentId.value ? { departmentId: currentDepartmentId.value } : {};
  leftList.forEach(({ url }, index) => {
    request.get(url, params).then((res) => {
      if (res.code === 200) {
        data.value[index] = res.data || {};
      }
    });
  });
};

const onClickTab = (index) => {
  activeTab.value = index;
  // 切换tab时重置滚动状态
  nextTick(() => {
    resetScroll();
  });
  // getData();
};

// 格式化标段名称，将连接符-替换为~
const formatSectionName = (sectionName) => {
  if (!sectionName) return "";

  // 将-替换为~
  return sectionName.replace(/-/g, " ~ ");
};

const peopleManagementModalRef = ref(null);

const onInfo = () => {
  peopleManagementModalRef.value?.open();
};
</script>

<style lang="scss" scoped>
.peoplesafe-root {
  display: flex;
  flex-direction: column;
  height: 247px;

  .safe-top {
    display: flex;
    justify-content: space-around;
    margin-bottom: 12px;
    padding: 0 8px;
    flex-shrink: 0; // 防止统计卡片区域被压缩
    height: 56px; // 设置固定高度，确保布局稳定

    .safe-card {
      display: flex;
      align-items: center;
      // background: rgba(30, 60, 100, 0.15);
      border-radius: 8px;
      padding: 8px 10px;
      flex: 1;
      margin: 0 4px;

      .safe-icon {
        width: 40px;
        height: 40px;
        margin-right: 8px;
        background-image: url("@/assets/modules/traffic/icon/device-bg.svg");
        background-size: 100% 100%;
        position: relative;

        img {
          position: absolute;
          left: 50%;
          bottom: 17px;
          transform: translateX(-50%);
          width: 20px;
          height: 20px;
        }
      }

      .safe-info {
        display: flex;
        flex-direction: column;

        .safe-name {
          font-family: Alibaba PuHuiTi;
          font-size: 12px;
          color: #c4e5ff;
          margin-bottom: 2px;
        }

        .safe-value {
          .num {
            // color: #fff;
            font-family: D-DIN-PRO, D-DIN-PRO;
            font-size: 18px;
            font-weight: bold;
            margin-right: 2px;
            text-shadow: 0px 0px 10px rgba(0, 198, 255, 0.25);
            background: -webkit-linear-gradient(top, #f7fdfd 0%, rgba(0, 198, 255, 0.25) 100%);
            background: linear-gradient(to bottom, #f7fdfd 0%, rgba(0, 198, 255, 0.25) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }

          .unit {
            font-family: Alibaba PuHuiTi;
            font-size: 10px;
            color: #c4e5ff;
          }
        }
      }

      &.card-picker {
        --border-size: 8px;
        --dot-size: 4px;
        --dot-offset: 4px;
        --bg-padding: 2px;
      }
    }
  }

  .safe-bottom {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; // 确保flex子元素可以收缩
    overflow: hidden; // 防止内容溢出

    .safe-progress {
      // display: flex;
      // align-items: center;
      margin-bottom: 8px;
      padding: 0 8px;
      flex-shrink: 0; // 防止进度条被压缩

      .safe-progress-title {
        display: flex;
        justify-content: space-between;
        align-items: center;

        &-l {
          font-weight: normal;
          font-size: 12px;
          color: #ffffff;
        }

        &-r {
          font-size: 12px;
          color: #ffffff;
        }

        &-v {
          font-family: D-DIN-PRO, D-DIN-PRO;
          font-weight: bold;
          font-size: 18px;
          text-shadow: 0px 0px 10px #27f3bd;
          background: -webkit-linear-gradient(top, #ffffff 0%, #27f3bd 100%);
          background: linear-gradient(to bottom, #ffffff 0%, #27f3bd 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          // background: linear-gradient(90deg, #FFFFFF 0%, #27F3BD 100%);
        }
      }

      .progress-bar {
        // flex: 1;
        width: 100%;
        height: 4px;
        background: #0f304e;
        // border-radius: 2px;
        margin-top: 4px;

        .progress-inner {
          height: 100%;
          background: linear-gradient(90deg, #003728 0%, #38e6fd 100%);
          // border-radius: 2px;
        }
      }

      .page-info {
        color: #38e6fd;
        font-size: 14px;
        min-width: 40px;
        text-align: right;
      }
    }

    .safe-table {
      display: flex;
      flex-direction: column;
      flex: 1;
      background: rgba(30, 60, 100, 0.1);
      overflow: hidden;
      margin: 0 8px;
      min-height: 0; // 确保flex子元素可以收缩
      // border-radius: 8px;
      // padding: 0px 12px 5px 8px;

      .scroll-container {
        flex: 1;
        overflow: hidden; // 确保滚动容器不会溢出
        min-height: 0; // 允许容器收缩到内容高度以下
      }

      .table-header {
        font-size: 12px;
        color: #ffffff;
        line-height: 32px;
        display: flex;
        align-items: center;
        flex-shrink: 0; // 防止表头被压缩
        background: linear-gradient(180deg, rgba(0, 138, 255, 0) 47%, #008aff 100%),
          rgba(0, 138, 255, 0.1);
        box-shadow: inset 0px 0px 8px 1px rgba(0, 138, 255, 0.25);
        border-radius: 0px 0px 0px 0px;
        border: 1px solid #008aff;
        border-image: linear-gradient(
            90deg,
            rgba(0, 138, 255, 0),
            rgba(0, 138, 255, 1),
            rgba(0, 138, 255, 0.2),
            rgba(0, 138, 255, 0)
          )
          1 1;
        padding: 0 8px;

        span {
          flex: 1;
          text-align: center;
        }
      }

      .table-row {
        display: flex;
        align-items: center;
        padding: 8px;
        color: #fff;
        font-size: 12px;
        min-height: 40px;
        border-bottom: 1px solid rgba(0, 138, 255, 0.3);

        &:last-child {
          border-bottom: 1px solid rgba(0, 138, 255, 0.3);
        }

        span {
          flex: 1;
          text-align: center;
        }

        .section-name {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          line-height: 1.4;
          padding: 4px 0;

          div {
            font-size: 12px;
            color: #fff;
            white-space: nowrap;
            margin: 1px 0;

            &:first-child {
              margin-top: 0;
            }

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        .status {
          // display: flex;
          text-align: right;
          // align-items: center;

          .dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            margin-right: 4px;
            display: inline-block;
            vertical-align: middle;
            // flex-shrink: 0;
            // flex-grow: 0;
            // flex-basis: 8px;
            box-sizing: border-box;
            &.passed {
              background: #27f3bd;
              box-shadow: 0px 0px 6px 0px rgba(41, 255, 198, 0.6);
            }

            &.pending {
              background: #f3b927;
              box-shadow: 0px 0px 6px 0px rgba(255, 194, 41, 0.6);
            }
          }

          .status-passed {
            color: #27f3bd;
          }

          .status-pending {
            color: #f3b927;
          }
        }
      }
    }
  }
}
</style>
