<template>
	<main-layout
		ref="mainLayoutRef"
		:class="{
			oneMap: moduleVisibility.oneMap,
			traffic: moduleVisibility.traffic,
			PartyBuilding: moduleVisibility.PartyBuilding,
		}"
		:showLeftSidebar="sidebarVisibility.left"
		:showRightSidebar="sidebarVisibility.right"
		:showLeftToggle="moduleVisibility.oneMap && oneMapMode === 1"
	>
		<template #header>
			<BasicPageHeader class="top-header">
				<template #mid>
					<div class="bottom-menu">
						<mMenu :default-active="mMenuActive" @select="handleMenuSelect">
							<mMenuItem index="1">一图统管</mMenuItem>
							<mMenuItem index="2" disabled>BIM贯通</mMenuItem>
							<mMenuItem index="3">保通</mMenuItem>
							<mMenuItem index="4" disabled>冻土</mMenuItem>
							<mMenuItem index="6">党建</mMenuItem>
							<mMenuItem index="7">大事记</mMenuItem>
						</mMenu>
					</div>
				</template>
				<template #right>
					<div class="top-header-right">
						<News />
						<WeatherWidget />
						<div class="top-header-right-divider" />
						<ClockWidget class="clock-widget-wrap" />
					</div>
				</template>
			</BasicPageHeader>
		</template>

		<template #left-sidebar>
			<template v-if="currentEngine === 'maplibre'"> </template>

			<template v-else-if="currentEngine === 'unreal'">
				<LayerTree class="layer-tree-wrap" />
				<Report class="report-wrap" />
			</template>

			<div class="sidebar-content left-wrap">
				<template v-if="moduleVisibility.oneMap">
					<OneMapLeftWrap />
				</template>
				<template v-if="moduleVisibility.traffic">
					<CurrentTime />
					<WeatherInfo />
					<IncidentMonitor />
					<EquipmentOnline />
				</template>
				<template v-if="moduleVisibility.permafrost">
					<ProjectStat />
					<AlarmStat />
					<MeasurePoint />
				</template>
				<template v-if="moduleVisibility.permafrost2">
					<MonitorCategory />
				</template>
				<template v-if="moduleVisibility.PartyBuilding">
					<OrganizationAnalysis />
					<PersonnelDistribution />
				</template>
			</div>
		</template>

		<template #right-sidebar>
			<div class="sidebar-content right-wrap">
				<template v-if="moduleVisibility.oneMap">
					<OneMapRightWrap />
				</template>
				<template v-if="moduleVisibility.traffic">
					<RealTimeFlow />
					<RealTimeSituation />
					<JamWarning />
				</template>
				<template v-if="moduleVisibility.permafrost">
					<PointInfo />
					<MonitorVideo />
					<PersonPatrol />
				</template>
				<template v-if="moduleVisibility.permafrost2">
					<MonitorOverview />
					<MonitorRank />
					<TimeCurve />
				</template>
				<template v-if="moduleVisibility.PartyBuilding">
					<GroupDynamics />
					<GrassrootsDynamics />
					<GrassrootsPartyBuilding />
				</template>
			</div>

			<div class="tool-bar-wrap">
				<ToolBar />

				<!-- 引擎切换按钮 -->
				<!-- <template v-if="moduleVisibility.oneMap">
					<template v-if="oneMapMode === 0">
						<div class="engine-switch-btn" @click="handleEngineSwitch()">
							<span class="engine-switch-btn-text">
								{{ currentEngine === "maplibre" ? "3D" : "2D" }}
							</span>
						</div>
					</template>
				</template> -->
			</div>
		</template>

		<template #default>
			<div v-show="moduleVisibility.oneMap || moduleVisibility.traffic">
				<engineScene ref="mapSceneRef" v-model="currentEngine"> </engineScene>
				<!-- loading动画 -->
				<div class="loading">
					<div class="loading-text">
						<span style="--index: 1">L</span>
						<span style="--index: 2">O</span>
						<span style="--index: 3">A</span>
						<span style="--index: 4">D</span>
						<span style="--index: 5">I</span>
						<span style="--index: 6">N</span>
						<span style="--index: 7">G</span>
					</div>
					<div class="loading-progress">
						<span class="value">{{ state.progress }}</span>
						<span class="unit">%</span>
					</div>
				</div>
			</div>
			<template v-if="moduleVisibility.oneMap">
				<PeoHeadTab />
			</template>
			<template v-if="moduleVisibility.dashiji">
				<DSJIndex />
			</template>
		</template>

		<template #mid>
			<template v-if="moduleVisibility.PartyBuilding">
				<div class="mid-content">
					<PartyMember />
					<OrganizationSituation />
				</div>
			</template>
		</template>

		<template #footer>
			<template v-if="moduleVisibility.oneMap">
				<PeoFooterTab />
			</template>
			<template v-if="moduleVisibility.traffic">
				<TrafficToolBar />
			</template>
		</template>

		<template #extra>
			<div v-show="moduleVisibility.permafrost && extraElementsVisible">
				<div class="dongtu-switch">
					<div class="switch-item left" @click="handleMenuSelect('4')">
						<img src="@/assets/images/dongtu/overview-selected.png" alt="" />
					</div>
					<div class="switch-item right" @click="handleMenuSelect('5')">
						<img src="@/assets/images/dongtu/realtime.png" alt="" />
					</div>
				</div>
				<!-- <EarlyWarning /> -->
			</div>
			<div v-show="moduleVisibility.permafrost2 && extraElementsVisible">
				<div class="dongtu-switch">
					<div class="switch-item left" @click="handleMenuSelect('4')">
						<img src="@/assets/images/dongtu/overview.png" alt="" />
					</div>
					<div class="switch-item right" @click="handleMenuSelect('5')">
						<img src="@/assets/images/dongtu/realtime-selected.png" alt="" />
					</div>
				</div>
				<SelectPoint />
				<DataOverview />
			</div>
		</template>
	</main-layout>

	<div id="teleport-container"></div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount } from "vue";
import { useRouter, useRoute } from "vue-router";
import MainLayout from "./components/layout/layout.vue";
import { useModuleAnimation } from "./utils/moduleAnimation"; // 导入动画系统
import { useAppStore } from "@/store";

import mMenu from "@/components/mMenu/index.vue";
import mMenuItem from "@/components/mMenuItem/index.vue";
import ToolBar from "./components/ToolBar.vue";

import BasicPageHeader from "@/components/BasicPageHeader/index.vue";
import ClockWidget from "@/components/ClockWidget/index.vue";
import WeatherWidget from "@/components/WeatherWidget/index.vue";
import EngineScene from "./engine/index.vue";
import News from "./components/News.vue";

// 一图统管组件
import PeoHeadTab from "./components/oneMap/PeoHeadTab.vue";
import PeoFooterTab from "./components/oneMap/PeoFooterTab.vue";
import OneMapLeftWrap from "./components/oneMap/LeftWrap.vue";
import OneMapRightWrap from "./components/oneMap/RightWrap.vue";

// 保通组件
import CurrentTime from "./components/traffic/CurrentTime.vue";
import WeatherInfo from "./components/traffic/WeatherInfo.vue";
import IncidentMonitor from "./components/traffic/IncidentMonitor.vue";
import EquipmentOnline from "./components/traffic/EquipmentOnline.vue";
import RealTimeFlow from "./components/traffic/RealTimeFlow.vue";
import RealTimeSituation from "./components/traffic/RealTimeSituation.vue";
import JamWarning from "./components/traffic/JamWarning.vue";
import TrafficToolBar from "./components/traffic/TrafficToolBar.vue";

// 冻土组件
import ProjectStat from "./components/dongtu/ProjectStat.vue";
import AlarmStat from "./components/dongtu/AlarmStat.vue";
import MeasurePoint from "./components/dongtu/MeasurePoint.vue";
import SelectPoint from "./components/dongtu/SelectPoint.vue";
import PointInfo from "./components/dongtu/PointInfo.vue";
import MonitorVideo from "./components/dongtu/MonitorVideo.vue";
import PersonPatrol from "./components/dongtu/PersonPatrol.vue";
import EarlyWarning from "./components/dongtu/EarlyWarning.vue";
import MonitorCategory from "./components/dongtu/MonitorCategory.vue";
import MonitorOverview from "./components/dongtu/MonitorOverview.vue";
import MonitorRank from "./components/dongtu/MonitorRank.vue";
import TimeCurve from "./components/dongtu/TimeCurve.vue";
import DataOverview from "./components/dongtu/DataOverview.vue";

//党建
import OrganizationAnalysis from "./components/PartyBuilding/OrganizationAnalysis.vue";
import PersonnelDistribution from "./components/PartyBuilding/PersonnelDistribution.vue";
import GroupDynamics from "./components/PartyBuilding/GroupDynamics.vue";
import GrassrootsDynamics from "./components/PartyBuilding/GrassrootsDynamics.vue";
import GrassrootsPartyBuilding from "./components/PartyBuilding/GrassrootsPartyBuilding.vue";
import PartyMember from "./components/PartyBuilding/PartyMember.vue";
import OrganizationSituation from "./components/PartyBuilding/OrganizationSituation.vue";

//大事记
import DSJIndex from "./components/dashiji/index.vue";

import Report from "./engine/components/UnrealEngine/components/Report.vue";
import LayerTree from "./engine/components/UnrealEngine/components/LayerTree.vue";
import emitter from "@/utils/emitter";
import gsap from "gsap";

// 添加路由相关
const router = useRouter();
const route = useRoute();
const mapSceneRef = ref(null);
const state = reactive({
	// 进度
	progress: 0,
	// 当前顶部导航索引
	activeIndex: "1",
});
const AppStore = useAppStore();

const mMenuActive = computed(() => {
	if (state.activeIndex == "5") return "4";
	return state.activeIndex;
});

const mainLayoutRef = ref(null);

// 模块显示状态
const moduleVisibility = reactive({
	oneMap: true, // 一图统管
	bim: false, // BIM贯通
	traffic: false, // 保通
	permafrost: false, // 冻土-监测总览
	permafrost2: false, // 冻土-实时监测
	PartyBuilding: false, // 党建
	dashiji: false, //大事记
});

// 菜单索引与模块的映射关系
const menuModuleMap = {
	1: "oneMap",
	2: "bim",
	3: "traffic",
	4: "permafrost",
	5: "permafrost2",
	6: "PartyBuilding",
	7: "dashiji",
};

const currentNav = ref({ text: "全览", value: "all" });
const oneMapMode = ref(0);
const navItems = [
	{ text: "全览", value: "all" },
	{ text: "格贡一", value: "gegong1" },
	{ text: "格贡二", value: "gegong2" },
	{ text: "格贡三", value: "gegong3" },
	{
		text: "试验段",
		value: "shiyanduan",
		disabled: false,
		children: [
			{ text: "五道梁试验段", value: "wudaoliang" },
			{ text: "高速试验段", value: "gaosu" },
			{ text: "贡玛日试验段", value: "gongmari" },
			{ text: "唐荣藏占试验段", value: "tangrongzangzhan" },
		],
	},
	{ text: "格贡四", value: "gegong4" },
	{ text: "贡那一", value: "gongna1" },
	{ text: "贡那二", value: "gongna2" },
];

watch(
	() => state.activeIndex,
	(newVal) => {
		emitter.$emit("menu-active", menuModuleMap[newVal]);
		AppStore.updateStore({ menu: menuModuleMap[newVal] });
	},
	{
		immediate: true,
	}
);

const handleNavClick = (item, index) => {
	// 更新当前选中的导航项
	currentNav.value = item;

	// 通过emitter发送导航切换事件，传递导航项信息
	emitter.$emit("pageNavChange", item.value);
};

// 隐藏loading
async function hideLoading() {
	return new Promise((resolve, reject) => {
		let tl = gsap.timeline();
		tl.to(".loading-text span", {
			y: "200%",
			opacity: 0,
			ease: "power4.inOut",
			duration: 2,
			stagger: 0.2,
		});
		tl.to(".loading-progress", { opacity: 0, ease: "power4.inOut", duration: 2 }, "<");
		tl.to(
			".loading",
			{
				opacity: 0,
				ease: "power4.inOut",
				onComplete: () => {
					resolve();
				},
			},
			"-=1"
		);
	});
}

// 添加侧边栏显示状态控制
const sidebarVisibility = reactive({
	left: true,
	right: true,
});

// 添加额外元素显示状态控制
const extraElementsVisible = ref(true);

// 全局切换所有UI元素
const toggleAllUIElements = (isVisible) => {
	// 创建动画时间线
	const tl = gsap.timeline();

	if (isVisible) {
		// 底部菜单显示动画
		tl.to(
			".bottom-menu",
			{
				opacity: 1,
				y: 0,
				duration: 0.5,
				ease: "power2.out",
			},
			0
		);

		tl.to(
			".dongtu-switch",
			{
				opacity: 1,
				scale: 1,
				duration: 0.5,
				ease: "back.out(1.7)",
			},
			0.1
		);

		// 动画完成后更新状态
		tl.call(() => {
			extraElementsVisible.value = true;
		});
	} else {
		// 先执行隐藏动画，再更新状态

		// 底部菜单隐藏动画
		tl.to(
			".bottom-menu",
			{
				opacity: 0,
				y: 50,
				duration: 0.4,
				ease: "power2.in",
			},
			0
		);

		// 导航和图例隐藏动画
		tl.to(
			".dongtu-switch",
			{
				opacity: 0,
				scale: 0.9,
				duration: 0.4,
				ease: "back.in(1.7)",
			},
			0
		);

		// 动画完成后更新状态
		tl.call(() => {
			extraElementsVisible.value = false;
		});
	}

	// 发送事件通知工具栏更新按钮状态
	emitter.$emit("uiElementsToggled", isVisible);
};

// 菜单与侧边栏显示状态的映射
const menuSidebarMap = {
	// 一张图
	1: { left: true, right: true },
	// BIM贯通
	2: { left: false, right: false },
	// 保通
	3: { left: true, right: true },
	// 冻土
	4: { left: true, right: true },
	5: { left: true, right: true },
	// 党建
	6: { left: true, right: true },
	// 大事记
	7: { left: false, right: false },
};

// 创建动画系统实例
const animationSystem = useModuleAnimation(sidebarVisibility);

function handleMenuSelect(index) {
	// 如果点击的是当前已选中的菜单项，则不执行任何操作
	if (state.activeIndex === index) {
		return;
	}

	// 获取当前模块和目标模块
	const currentModuleKey = menuModuleMap[state.activeIndex];
	const targetModuleKey = menuModuleMap[index];

	state.activeIndex = index;

	// 使用动画系统切换模块
	animationSystem.switchModule(
		currentModuleKey,
		targetModuleKey,
		() => {
			// 更新侧边栏显示状态
			updateSidebarVisibility(index);

			// 更新模块可见性
			Object.keys(moduleVisibility).forEach((key) => {
				moduleVisibility[key] = key === targetModuleKey;
			});
			// 更新URL参数
			updateUrlParams(index);
		},

		() => {}
	);
}

function handleOneMapModeChange(index) {
	oneMapMode.value = index;
	if (index === 0) {
		mainLayoutRef.value?.toggleSidebarState({
			side: "both",
			state: "open",
		});
	} else if (index === 1) {
		mainLayoutRef.value?.toggleSidebarState({
			side: "both",
			state: "close",
		});
	}
}

// 从URL参数初始化页面 - 优化后的实现
function initFromUrlParams() {
	const pageParam = route.query.page;
	if (pageParam) {
		// 根据URL参数设置当前活跃的菜单
		for (const [key, value] of Object.entries(menuModuleMap)) {
			if (value === pageParam) {
				state.activeIndex = key;

				// 设置侧边栏显示状态
				const sidebarConfig = menuSidebarMap[key];
				if (sidebarConfig) {
					sidebarVisibility.left = sidebarConfig.left;
					sidebarVisibility.right = sidebarConfig.right;
				}

				break;
			}
		}

		// 先重置所有模块可见性
		Object.keys(moduleVisibility).forEach((key) => {
			moduleVisibility[key] = false;
		});

		// 设置当前模块可见
		moduleVisibility[pageParam] = true;

		// 初始化时显示模块
		const moduleKey = pageParam;

		// 使用动画系统显示初始模块
		setTimeout(() => {
			const tl = animationSystem.createTimeline();
			animationSystem.fadeInModule(tl, moduleKey, () => {
				// 显示侧边栏按钮
				setTimeout(() => {
					animationSystem.showSidebarButtons();
				}, 200);
			});
		}, 100);
	} else {
		// 默认显示一张图模块
		setTimeout(() => {
			const tl = animationSystem.createTimeline();
			animationSystem.fadeInModule(tl, "oneMap", () => {
				//
			});
		}, 100);
	}
}

// 更新侧边栏显示状态
function updateSidebarVisibility(menuIndex) {
	const sidebarConfig = menuSidebarMap[menuIndex];

	if (sidebarConfig) {
		// 更新状态
		sidebarVisibility.left = sidebarConfig.left;
		sidebarVisibility.right = sidebarConfig.right;

		sidebarConfig.left &&
			mainLayoutRef.value?.toggleSidebarState({
				side: "left",
				state: "open",
			});

		sidebarConfig.right &&
			mainLayoutRef.value?.toggleSidebarState({
				side: "right",
				state: "open",
			});
	}
}

// 更新URL参数
function updateUrlParams(menuIndex) {
	const moduleKey = menuModuleMap[menuIndex];

	if (moduleKey && moduleKey !== "oneMap") {
		// 如果不是默认模块，添加参数
		router.replace({ query: { ...route.query, page: moduleKey } });
	} else {
		// 如果是默认模块，移除参数
		const newQuery = { ...route.query };
		delete newQuery.page;
		router.replace({ query: newQuery });
	}
}

// @TODO 在当前模块下可以指定默认的引擎 进行引擎切换
// 默认引擎加载完成显示所有元素及loading 100% ~ 隐藏loading
// 切换引擎时：引擎加载完成，监听引擎状态，显示引擎元素及loading 100% ~ 隐藏loading
// 重复切换时loading 100% ~ 隐藏loading

const currentEngine = ref("maplibre"); // 默认为二维引擎

// 存储当前活跃的清理函数
let currentCleanupLoading = null;

// 处理引擎切换按钮点击
async function handleEngineSwitch(targetEngine, callback) {
	if (!targetEngine) {
		targetEngine = currentEngine.value === "maplibre" ? "unreal" : "maplibre";
	}

	// 如果目标引擎与当前引擎相同，则直接执行回调
	if (targetEngine === currentEngine.value && callback) {
		callback(mapSceneRef.value);
		return true;
	}

	// 显示加载动画
	document.querySelector(".loading").style.opacity = "1";
	document.querySelector(".loading").style.display = "flex";

	// 清理之前的加载进度监听（如果存在）
	if (currentCleanupLoading) {
		currentCleanupLoading();
		currentCleanupLoading = null;
	}

	// 重置并启动加载进度条
	currentCleanupLoading = initTimeBasedLoading((engineData) => {
		console.log("引擎切换完成:", engineData);

		// 引擎切换成功后执行回调
		if (callback && typeof callback === "function") {
			setTimeout(() => {
				callback(mapSceneRef.value);
			}, 500); // 给引擎一点时间完全初始化
		}
	});

	// 切换引擎
	const result = await mapSceneRef.value?.switchEngine?.(targetEngine);

	// 如果切换失败，手动清理加载动画
	if (!result) {
		if (currentCleanupLoading) {
			currentCleanupLoading();
			currentCleanupLoading = null;
		}
		// 快速隐藏加载界面
		hideLoading();
		return false;
	}

	// 切换成功，更新当前引擎状态
	currentEngine.value = targetEngine;
	return true;
}

// 基于时间的加载进度条
function initTimeBasedLoading(engineReadyCallback) {
	// 初始化进度
	state.progress = 0;

	// 重置加载动画文本元素样式
	const loadingTextSpans = document.querySelectorAll(".loading-text span");
	loadingTextSpans.forEach((span) => {
		span.style.opacity = "1";
		span.style.transform = "translateY(0)";
	});

	// 重置进度显示元素
	const progressElement = document.querySelector(".loading-progress");
	if (progressElement) {
		progressElement.style.opacity = "1";
	}

	// 创建时间线动画
	const loadingTimeline = gsap.timeline({
		paused: true,
		onComplete: () => {
			console.log("加载动画时间线完成");
		},
	});

	// 第一阶段：快速增长到60%
	loadingTimeline.to(state, {
		progress: 60,
		duration: 2,
		ease: "power2.out",
		onUpdate: () => {
			// 确保进度为整数
			state.progress = Math.floor(state.progress);
		},
	});

	// 第二阶段：缓慢增长到85%
	loadingTimeline.to(state, {
		progress: 85,
		duration: 3,
		ease: "power1.inOut",
		onUpdate: () => {
			// 确保进度为整数
			state.progress = Math.floor(state.progress);
		},
	});

	// 第三阶段：更缓慢增长到95%
	loadingTimeline.to(state, {
		progress: 95,
		duration: 4,
		ease: "sine.inOut",
		onUpdate: () => {
			// 确保进度为整数
			state.progress = Math.floor(state.progress);
		},
	});

	// 启动时间线
	loadingTimeline.play();

	// 创建唯一的事件处理函数
	const onEngineLoadHandler = (engineData) => {
		// 停止时间线动画
		loadingTimeline.kill();

		// 快速将进度设为100%
		gsap.to(state, {
			progress: 100,
			duration: 0.5,
			ease: "power4.out",
			onUpdate: () => {
				// 确保进度为整数
				state.progress = Math.floor(state.progress);
			},
			onComplete: () => {
				// 确保最终进度为100%整数
				state.progress = 100;
				// 延迟一小段时间后隐藏加载界面
				setTimeout(() => {
					hideLoading().then(() => {
						// 加载完成后执行回调
						engineReadyCallback && engineReadyCallback(engineData);
					});
				}, 300);
			},
		});
	};

	// 添加事件监听
	emitter.$on("engineOnLoad", onEngineLoadHandler);

	// 返回一个清理函数
	return () => {
		console.log("清理加载进度监听");
		loadingTimeline.kill();
		emitter.$off("engineOnLoad", onEngineLoadHandler);
	};
}

onMounted(() => {
	// 设置主题
	document.body.setAttribute("arco-theme", "dark");
	// 通知其他模块
	emitter.$emit("pageNavChange", currentNav.value.value);

	// 从URL参数初始化当前页面
	initFromUrlParams();

	initTimeBasedLoading();

	// 添加工具栏全局切换事件监听
	emitter.$on("toggleSidebars", (isCollapsed) => {
		mainLayoutRef.value?.toggleSidebarState({
			state: !isCollapsed ? "open" : "close",
		});
		// toggleAllUIElements(!isCollapsed);
	});

	// 添加引擎切换事件监听
	emitter.$on("switchEngine", ({ targetEngine, callback }) => {
		handleEngineSwitch(targetEngine, callback);
	});

	// 一图统管模式切换监听
	emitter.$on("header-tab-change", handleOneMapModeChange);
});

onBeforeUnmount(() => {
	// 清理当前活跃的加载监听
	if (currentCleanupLoading) {
		currentCleanupLoading();
		currentCleanupLoading = null;
	}
	emitter.$off("engineOnLoad");
	// 移除事件监听
	emitter.$off("toggleSidebars");
	// 移除引擎切换事件监听
	emitter.$off("switchEngine");

	emitter.$off("header-tab-change", handleOneMapModeChange);
});
</script>

<style lang="scss">
@use "~@/assets/style/home.scss";

.bottom-menu {
	position: relative;
	display: flex;
	justify-content: center;
}

// 修改底部托盘动画
.bottom-tray {
	transform: translateY(100%);
	opacity: 0;
	padding-top: 10px;
}
.bottom-radar {
	transform: translateY(100%);
	opacity: 0;
}

.top-header {
	&-right {
		display: flex;
		align-items: center;
		margin-top: 10px;
		&-divider {
			width: 0px;
			height: 27px;
			margin: 0 24px;
			border: 0.5px solid var(--header-right-text-color);
		}
	}
	// .clock-widget-wrap {
	// 	margin-left: 60px;
	// }
}

.sidebar-content {
	display: flex;
	flex-direction: column;
	width: var(--sidebar-width);
	height: 100%;
	overflow: hidden;
	&.left-wrap {
		margin-left: var(--sidebar-margin);
	}

	&.right-wrap {
		margin-right: var(--sidebar-margin);
	}
}

.mid-content {
	width: auto;
	height: 100%;
	margin: 0 550px;
	padding: 0 40px;
}

.page-navigation-wrap {
	position: absolute;
	top: 84px;
	left: 514px;
	z-index: 3;
	pointer-events: auto;
}

.map-legend-wrap {
	position: absolute;
	left: 514px;
	bottom: 52px;
	z-index: 3;
	pointer-events: auto;
}
.dongtu-switch {
	position: absolute;
	top: 75px;
	left: 543px;
	right: 543px;
	height: 39px;
	z-index: 3;
	.switch-item {
		position: absolute;
		width: 188px;
		height: 39px;
		cursor: pointer;
		pointer-events: all;
		img {
			width: 100%;
			height: 100%;
		}
		&.left {
			top: 0;
			left: 0;
		}
		&.right {
			top: 0;
			right: 0;
		}
	}
}
// 引擎切换按钮样式
.engine-switch-btn {
	position: absolute;
	top: 0;
	right: 505px;
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	cursor: pointer;
	z-index: 10;
	transition: all 0.3s;
	background: linear-gradient(180deg, rgba(0, 138, 255, 0) 47%, #008aff 100%),
		radial-gradient(0% 87% at -13% 112%, #64c6ff 0%, rgba(8, 62, 115, 0) 100%),
		rgba(0, 138, 255, 0.3);
	box-shadow: inset 0px 0px 8px 0px rgba(0, 138, 255, 0.25);
	border: 1px solid;
	border-radius: 4px;
	border-image: linear-gradient(
			90deg,
			rgba(0, 138, 255, 0),
			rgba(0, 138, 255, 1),
			rgba(0, 138, 255, 0.2),
			rgba(0, 138, 255, 0)
		)
		1 1;
	pointer-events: auto;

	&-text {
		font-family: Alibaba PuHuiTi;
		font-size: 18px;
		font-weight: 700;
		color: #d5e2ff;
		line-height: 38px;
	}
}

.tool-bar-wrap {
	position: absolute;
	right: 494px;
	top: 0;
}

.report-wrap {
	position: absolute;
	left: 444px;
	bottom: 52px;
	z-index: 3;
	pointer-events: auto;
}
.layer-tree-wrap {
	position: absolute;
	top: 0;
	left: 444px;
	z-index: 3;
	pointer-events: auto;
}
</style>
