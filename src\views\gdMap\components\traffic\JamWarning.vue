<template>
	<TrafficCard title="交通拥堵预警">
		<div class="jam-warning">
			<div class="target-info">
				<div class="target-item">
					<div class="target-icon">
						<alarmBellDanger />
					</div>
					<div class="target-content">
						<div class="target-value">{{ getNum("2") }}</div>
						<div class="target-label">异常拥堵</div>
					</div>
				</div>
				<div class="target-item">
					<div class="target-icon">
						<alarmBellWarning />
					</div>
					<div class="target-content">
						<div class="target-value">{{ getNum("3") }}</div>
						<div class="target-label">常规拥堵</div>
					</div>
				</div>
				<div class="target-item">
					<div class="target-icon">
						<alarmBellPrimary />
					</div>
					<div class="target-content">
						<div class="target-value">{{ getNum("1") }}</div>
						<div class="target-label">高疑似拥堵</div>
					</div>
				</div>
			</div>
			<div class="table-container">
				<div class="table-header">
					<div class="table-title">拥堵预警</div>
					<div class="table-tab-list">
						<div
							v-for="item in tabList"
							:key="item.value"
							class="table-tab-list-item"
							:class="{ 'is-active': item.value === activeTab }"
							@click="onClickTab(item)"
						>
							{{ item.label }}
						</div>
					</div>
				</div>
				<div ref="containerRef" class="scroll-container">
					<div class="table-body">
						<div
							v-for="(item, index) in listData"
							:key="index"
							class="table-row"
							:class="item.dealStatus ? 'deal' : 'unDeal'"
							@click="onClickRow(item)"
						>
							<div class="table-col icon">
								<component :is="alarmTypeMap[item.eventType].icon" />
							</div>
							<div class="table-col type">{{ alarmTypeMap[item.eventType].label }}</div>
							<div class="table-col area">{{ item.station }}</div>
							<div class="table-col time">{{ item.startTime }}</div>
							<div class="table-col duration">拥堵持续{{ item.congestionDuration }}min</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</TrafficCard>
</template>

<script setup>
import request from "@/utils/request";
import { useMarquee } from "@/hooks/useMarquee";
import alarmBellDanger from "@/assets/modules/traffic/icon/alarmBell-danger.svg?component";
import alarmBellWarning from "@/assets/modules/traffic/icon/alarmBell-warning.svg?component";
import alarmBellPrimary from "@/assets/modules/traffic/icon/alarmBell-primary.svg?component";
import { IconDoubleRight } from "@arco-design/web-vue/es/icon";
import emitter from "@/utils/emitter";

const tabList = [
	{ label: "全部", value: "" },
	{ label: "异常拥堵", value: "2" },
	{ label: "常规拥堵", value: "3" },
	{ label: "高疑似拥堵", value: "1" },
];
const alarmTypeMap = {
	1: {
		icon: alarmBellDanger,
		label: "高疑似拥堵",
	},
	2: {
		icon: alarmBellDanger,
		label: "异常拥堵",
	},
	3: {
		icon: alarmBellPrimary,
		label: "常规拥堵",
	},
};
const activeTab = ref("");
const statusData = ref([]);
const listData = ref([]);
const currentDepartmentId = ref(null);

const { containerRef, resetScroll } = useMarquee({
	speed: 10, // 滚动速度
	delay: 2000, // 滚动到底部后停顿时间
	step: 1, // 每次滚动的像素
});

watch(
	listData,
	() => {
		setTimeout(resetScroll, 300); // 延迟一点时间确保DOM已更新
	},
	{ deep: true }
);

onMounted(() => {
	// 监听区域双击事件
	emitter.$on("division-area-dblclick", handleAreaChange);
	getData();
});

onBeforeUnmount(() => {
	emitter.$off("division-area-dblclick", handleAreaChange);
});

const handleAreaChange = (eventData) => {
	console.log("交通拥堵预警 - 接收到区域双击事件:", eventData);
	currentDepartmentId.value = eventData.properties.id;
	getData();
};

const getData = () => {
	getStatus();
	getList();
};

const getStatus = () => {
	const params = currentDepartmentId.value ? { departmentId: currentDepartmentId.value } : {};
	request.get("/api/screen/baotong/traffic/alarm/stat", params).then((res) => {
		if (res.code == 200) {
			statusData.value = res.data;
		}
	});
};

const getList = () => {
	const params = currentDepartmentId.value
		? { departmentId: currentDepartmentId.value, eventType: unref(activeTab) }
		: { eventType: unref(activeTab) };
	request.get("/api/screen/baotong/traffic/alarm/list", params).then((res) => {
		if (res.code == 200) {
			listData.value = res.data;
		}
	});
};

const getNum = (name) => {
	return unref(statusData.value).find((item) => item.name === name)?.num || 0;
};

const onClickTab = (tab) => {
	activeTab.value = tab.value;
	getList(); // 切换tab时重新获取列表数据
};

const onClickRow = (item) => {
	const options = {
		center: item.coordinates,
		zoom: 16,
	};
	emitter.$emit("locateToPosition", options);
};

const onViewDetails = () => {
  // 查看详情逻辑
  console.log('查看交通拥堵预警详情');
};
</script>

<style lang="scss" scoped>
:deep(.card-body) {
	background-color: transparent;
}

.jam-warning {
	height: 295px;
	display: flex;
	flex-direction: column;
}

.target-info {
	padding: 0 12px 12px;
	display: flex;
	justify-content: space-evenly;

	.target-item {
		display: flex;
		align-items: center;

		&:nth-of-type(1) .target-icon {
			background-color: rgba(203, 143, 143, 0.1);
			&::after {
				background-color: rgba(255, 143, 143, 0.22);
				border-color: #cb335c;
			}
		}

		&:nth-of-type(2) .target-icon {
			background-color: rgba(255, 173, 41, 0.1);
			&::after {
				background-color: rgba(255, 173, 41, 0.22);
				border-color: #ffad29;
			}
		}

		&:nth-of-type(3) .target-icon {
			background-color: rgba(23, 228, 255, 0.1);
			&::after {
				background-color: rgba(23, 228, 255, 0.22);
				border-color: #17e4ff;
			}
		}

		.target-icon {
			width: 44px;
			height: 44px;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			border-radius: 50%;

			&::after {
				content: "";
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				width: 32px;
				height: 32px;
				border: 2px solid;
				border-radius: 50%;
			}

			svg {
				width: 20px;
				height: 20px;
			}
		}

		.target-content {
			margin-left: 8px;
			display: flex;
			flex-direction: column;
			align-items: start;
			justify-content: center;
			height: 100%;

			.target-label {
				font-family: Alibaba PuHuiTi;
				font-weight: normal;
				font-size: 10px;
				color: #ffffff;
				white-space: nowrap; // 防止文本换行
			}

			.target-value {
				font-family: "D-DIN-PRO";
				font-weight: bold;
				font-size: 20px;
				color: #ffffff;
				white-space: nowrap; // 防止数值换行
			}
		}
	}
}

.table-container {
	flex: 1;
	overflow: hidden;
	display: flex;
	flex-direction: column;

	.scroll-container {
		height: 1px;
		flex: 1;
	}

	.table-header {
		height: 32px;
		background-image: linear-gradient(
			180deg,
			rgba(0, 138, 255, 0) 47%,
			rgba(0, 138, 255, 0.1) 100%
		);
		box-sizing: border-box;
		border: 1px solid;
		border-image: linear-gradient(
				90deg,
				rgba(0, 138, 255, 0),
				rgba(0, 138, 255, 1),
				rgba(0, 138, 255, 0.2),
				rgba(0, 138, 255, 0)
			)
			1 1;
		padding: 0 12px;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.table-title {
			font-family: Alibaba PuHuiTi;
			font-size: 16px;
			color: #ffffff;
		}

		.table-tab-list {
			display: flex;
			column-gap: 4px;

			&-item {
				width: 60px;
				height: 20px;
				display: flex;
				align-items: center;
				justify-content: center;
				font-family: Alibaba PuHuiTi;
				font-size: 12px;
				color: #ffffff;
				border-top: 1px solid transparent;
				cursor: pointer;

				&.is-active {
					background: linear-gradient(180deg, rgba(0, 138, 255, 0) 0%, #008aff 100%);
					border-color: #008aff;
				}
			}
		}
	}

	.table-body {
		.table-row {
			height: 28px;
			display: flex;
			align-items: center;
			padding: 0 8px;
			box-sizing: border-box;
			position: relative;

			&.deal {
				&::after {
					content: "";
					position: absolute;
					bottom: -1px;
					left: 8px;
					width: calc(100% - 16px);
					height: 0px;
					border-bottom: 1px solid rgba(0, 138, 255, 0.4);
				}

				.dealStatus {
					background: linear-gradient(180deg, #008aff 0%, #0064b8 100%);
				}
			}

<<<<<<< HEAD
      &.unDeal {
        border: 1px solid transparent;
        
        &:hover {
          background: rgba(220, 94, 98, 0.3);
          border-color: #cb335c;
        }
=======
			&.unDeal {
				/* background: rgba(220, 94, 98, 0.3); */
				/* border: 1px solid #cb335c; */
>>>>>>> eb04d00e54995f1a21150fffee001e8a988f4d5e

				& + .unDeal {
					border-top: none;
				}

				.dealStatus {
					background: linear-gradient(180deg, #ed5b80 0%, #cb335c 100%);
				}
			}

			.table-col {
				margin-left: 4px;
				font-family: Alibaba PuHuiTi;
				font-size: 12px;
				color: #ffffff;

				&.icon {
					svg {
						width: 16px;
						height: 16px;
					}
				}

				&.type {
					width: 70px;
				}

				&.area {
					flex: 1;
					text-align: left;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				&.time {
					width: 128px;
					text-align: left;
				}

				&.duration {
					width: 100px;
				}

				&.dealStatus {
					width: 54px;
					height: 20px;
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: 4px;
				}
			}
		}
	}
}

.link {
  display: flex;
  flex-direction: row;
  align-items: center;
  cursor: pointer;
}
</style>
